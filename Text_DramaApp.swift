//
//  Text_DramaApp.swift
//  Text_Drama
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/25.
//

import SwiftUI
import FirebaseCore
import FirebaseFirestore
import FirebaseStorage

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication,
                    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        FirebaseApp.configure()

        // Enable Firestore debug logging during development
        #if DEBUG
        let db = Firestore.firestore()
        let settings = db.settings
        db.settings = settings
        #endif

        return true
    }
}

@main
struct Text_DramaApp: App {
    // Register app delegate for Firebase setup
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}
