//
//  Text_DramaApp.swift
//  Text_Drama
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/25.
//

import SwiftUI
import FirebaseCore
import FirebaseFirestore
import FirebaseStorage
import FirebaseAuth
import RevenueCat

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication,
                    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // Configure Firebase
        FirebaseApp.configure()

        // Enable Firestore debug logging during development
        #if DEBUG
        let db = Firestore.firestore()
        let settings = db.settings
        db.settings = settings
        #endif

        // Initialize RevenueCat - this will be done in RevenueCatManager
        // RevenueCat configuration is handled in RevenueCatManager.shared initialization

        return true
    }
}

@main
struct Text_DramaApp: App {
    // Register app delegate for Firebase setup
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate

    // Initialize managers
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(authManager)
                .environmentObject(revenueCatManager)
                .environmentObject(subscriptionManager)
        }
    }
}
