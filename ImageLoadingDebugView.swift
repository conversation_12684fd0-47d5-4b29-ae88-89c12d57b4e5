import SwiftUI

struct ImageLoadingDebugView: View {
    @State private var testImageURL = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600"
    @State private var diagnosticsResult = ""
    @State private var isLoading = false
    @State private var testImage: UIImage?
    @StateObject private var networkDiagnostics = NetworkDiagnostics.shared

    private var connectionTypeString: String {
        guard let type = networkDiagnostics.connectionType else { return "Unknown" }
        switch type {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "Cellular"
        case .wiredEthernet:
            return "Ethernet"
        case .loopback:
            return "Loopback"
        case .other:
            return "Other"
        @unknown default:
            return "Unknown"
        }
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Network Status
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Network Status")
                            .font(.headline)

                        HStack {
                            Circle()
                                .fill(networkDiagnostics.isConnected ? Color.green : Color.red)
                                .frame(width: 12, height: 12)
                            Text(networkDiagnostics.isConnected ? "Connected" : "Disconnected")
                            Spacer()
                            Text(connectionTypeString)
                                .foregroundColor(.secondary)
                        }

                        #if targetEnvironment(simulator)
                        Text("Running in iOS Simulator")
                            .foregroundColor(.orange)
                            .font(.caption)
                        #else
                        Text("Running on Physical Device")
                            .foregroundColor(.green)
                            .font(.caption)
                        #endif
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                    // Image URL Test
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Image URL Test")
                            .font(.headline)

                        TextField("Image URL", text: $testImageURL)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        Button("Test Image Loading") {
                            testImageLoading()
                        }
                        .disabled(isLoading)

                        if isLoading {
                            ProgressView("Loading...")
                        }

                        if let image = testImage {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 200)
                                .cornerRadius(8)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                    // Quick Test Buttons
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Quick Tests")
                            .font(.headline)

                        VStack(spacing: 8) {
                            Button("Test Unsplash Images API") {
                                testImageURL = "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600"
                                testImageLoading()
                            }

                            Button("Test Unsplash Source (Problematic)") {
                                testImageURL = "https://source.unsplash.com/800x600/?nature"
                                testImageLoading()
                            }

                            Button("Test HTTP Image (Should Fail)") {
                                testImageURL = "http://httpbin.org/image/jpeg"
                                testImageLoading()
                            }
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                    // Diagnostics
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Network Diagnostics")
                                .font(.headline)
                            Spacer()
                            Button("Run Diagnostics") {
                                runDiagnostics()
                            }
                        }

                        if !diagnosticsResult.isEmpty {
                            ScrollView {
                                Text(diagnosticsResult)
                                    .font(.system(.caption, design: .monospaced))
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(maxHeight: 200)
                            .padding(8)
                            .background(Color.black.opacity(0.1))
                            .cornerRadius(4)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                    // Recommendations
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Simulator Troubleshooting")
                            .font(.headline)

                        VStack(alignment: .leading, spacing: 4) {
                            Text("• Check if Info.plist has NSAppTransportSecurity settings")
                            Text("• Verify simulator has internet connectivity")
                            Text("• Try resetting simulator network settings")
                            Text("• Check if Unsplash is blocking simulator requests")
                            Text("• Consider using alternative image sources for simulator")
                        }
                        .font(.caption)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }
                .padding()
            }
            .navigationTitle("Image Loading Debug")
        }
        .onAppear {
            runDiagnostics()
        }
    }

    private func testImageLoading() {
        isLoading = true
        testImage = nil

        FirebaseStorageService.shared.downloadImage(from: testImageURL) { image in
            DispatchQueue.main.async {
                self.isLoading = false
                self.testImage = image
            }
        }
    }

    private func runDiagnostics() {
        NetworkDiagnostics.shared.runDiagnostics { result in
            self.diagnosticsResult = result
        }
    }
}

#Preview {
    ImageLoadingDebugView()
}
