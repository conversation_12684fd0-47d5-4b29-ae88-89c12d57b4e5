import Foundation
import FirebaseFirestore
import Combine

class LikeService: ObservableObject {
    static let shared = LikeService()
    private let db = Firestore.firestore()
    
    private init() {}
    
    // Toggle like for a chat
    func toggleLike(for chatId: String, completion: @escaping (Bool, Int) -> Void) {
        guard let chatId = chatId as String? else {
            completion(false, 0)
            return
        }
        
        let likeRef = db.collection("chatLikes").document(chatId)
        
        // Get current like data
        likeRef.getDocument { [weak self] document, error in
            guard let self = self else { return }
            
            if let error = error {
                print("Error getting like document: \(error.localizedDescription)")
                completion(false, 0)
                return
            }
            
            var currentCount = 0
            var isCurrentlyLiked = false
            
            if let document = document, document.exists {
                let data = document.data()
                currentCount = data?["likeCount"] as? Int ?? 0
                isCurrentlyLiked = data?["isLiked"] as? Bool ?? false
            }
            
            // Toggle the like state
            let newIsLiked = !isCurrentlyLiked
            let newCount = newIsLiked ? currentCount + 1 : max(0, currentCount - 1)
            
            // Update Firebase
            let likeData: [String: Any] = [
                "likeCount": newCount,
                "isLiked": newIsLiked,
                "lastUpdated": Timestamp()
            ]
            
            likeRef.setData(likeData) { error in
                if let error = error {
                    print("Error updating like: \(error.localizedDescription)")
                    completion(false, currentCount)
                } else {
                    completion(newIsLiked, newCount)
                }
            }
        }
    }
    
    // Get like data for a chat
    func getLikeData(for chatId: String, completion: @escaping (Bool, Int) -> Void) {
        let likeRef = db.collection("chatLikes").document(chatId)
        
        likeRef.getDocument { document, error in
            if let error = error {
                print("Error getting like data: \(error.localizedDescription)")
                completion(false, 0)
                return
            }
            
            if let document = document, document.exists {
                let data = document.data()
                let isLiked = data?["isLiked"] as? Bool ?? false
                let likeCount = data?["likeCount"] as? Int ?? 0
                completion(isLiked, likeCount)
            } else {
                completion(false, 0)
            }
        }
    }
    
    // Listen to like changes for a chat
    func listenToLikeChanges(for chatId: String, completion: @escaping (Bool, Int) -> Void) -> ListenerRegistration {
        let likeRef = db.collection("chatLikes").document(chatId)
        
        return likeRef.addSnapshotListener { document, error in
            if let error = error {
                print("Error listening to like changes: \(error.localizedDescription)")
                completion(false, 0)
                return
            }
            
            if let document = document, document.exists {
                let data = document.data()
                let isLiked = data?["isLiked"] as? Bool ?? false
                let likeCount = data?["likeCount"] as? Int ?? 0
                completion(isLiked, likeCount)
            } else {
                completion(false, 0)
            }
        }
    }
}
