import Foundation
import FirebaseFirestore
import SwiftUI

struct ChatPreview: Identifiable, Codable, Equatable {
    // Implement Equatable
    static func == (lhs: ChatPreview, rhs: ChatPreview) -> Bool {
        return lhs.id == rhs.id
    }
    var id: String?
    let title: String
    let description: String
    let timestamp: Timestamp
    let messageCount: Int
    var categories: [String] = [] // Array of categories from Firebase
    var image_url: String? // URL for the background image
    var likeCount: Int = 0 // Number of likes for this chat
    var isLiked: Bool = false // Whether current user has liked this chat

    // Computed property to get the primary category for display
    var primaryCategory: String {
        return categories.first ?? "Uncategorized"
    }

    enum CodingKeys: String, CodingKey {
        case id
        case title
        case description
        case timestamp
        case messageCount
        case categories
        case image_url
        case likeCount
        case isLiked
    }

    // Custom initializer with categories array and optional image_url
    init(id: String? = nil, title: String, description: String, timestamp: Timestamp, messageCount: Int, categories: [String] = [], image_url: String? = nil, likeCount: Int = 0, isLiked: Bool = false) {
        self.id = id
        self.title = title
        self.description = description
        self.timestamp = timestamp
        self.messageCount = messageCount
        self.categories = categories
        self.image_url = image_url
        self.likeCount = likeCount
        self.isLiked = isLiked
    }

    // Custom decoder init to handle categories array and image_url
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decodeIfPresent(String.self, forKey: .id)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decode(String.self, forKey: .description)
        timestamp = try container.decode(Timestamp.self, forKey: .timestamp)
        messageCount = try container.decode(Int.self, forKey: .messageCount)

        // Try to decode categories array, use empty array if not present
        if container.contains(.categories) {
            categories = try container.decode([String].self, forKey: .categories)
        } else {
            categories = []
        }

        // Try to decode image_url, use nil if not present
        image_url = try container.decodeIfPresent(String.self, forKey: .image_url)

        // Try to decode like data, use defaults if not present
        likeCount = try container.decodeIfPresent(Int.self, forKey: .likeCount) ?? 0
        isLiked = try container.decodeIfPresent(Bool.self, forKey: .isLiked) ?? false
    }

    // Helper function to get color based on primary category
    func categoryColor() -> Color {
        switch primaryCategory.lowercased() {
        case "personal": return .blue
        case "work": return .green
        case "family": return .purple
        case "friends": return .orange
        case "important": return .red
        case "drama": return .purple
        case "thriller": return .red
        case "comedy": return .orange
        case "romance": return .pink
        case "sci-fi": return .blue
        case "horror": return .black
        default: return .gray
        }
    }
}
