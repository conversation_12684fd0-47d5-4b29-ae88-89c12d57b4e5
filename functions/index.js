/**
 * Import function triggers from their respective submodules:
 *
 * const {onCall} = require("firebase-functions/v2/https");
 * const {onDocumentWritten} = require("firebase-functions/v2/firestore");
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

const {onRequest} = require("firebase-functions/v2/https");
const {logger} = require("firebase-functions");
const admin = require("firebase-admin");

// Initialize Firebase Admin
admin.initializeApp();

// Create and deploy your first functions
// https://firebase.google.com/docs/functions/get-started

// Create and deploy a function (optional)
exports.helloWorld = onRequest((request, response) => {
  logger.info("Hello logs!", {structuredData: true});
  response.send("Hello from Firebase!");
});

// Function to create sample chat data
exports.createSampleData = onRequest(async (request, response) => {
  try {
    const db = admin.firestore();

    // Sample chat data with longer conversations
    const chats = [
      {
        id: "toxic-relationship-drama",
        title: "Toxic Relationship Drama",
        description: "A relationship that spiraled out of control over a simple Instagram like",
        timestamp: admin.firestore.Timestamp.now(),
        messageCount: 25,
        image_url: "https://firebasestorage.googleapis.com/v0/b/text-drama.firebasestorage.app/o/public_images%2Fchat_toxic-relationship.jpg?alt=media",
        messages: [
          { sender: "girlfriend", text: "Hey, we need to talk.", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 120 * 60000) },
          { sender: "boyfriend", text: "What's up?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 119 * 60000) },
          { sender: "girlfriend", text: "Why did you like Emily's beach photo from 2 years ago? 🤨", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 118 * 60000) },
          { sender: "boyfriend", text: "What? I didn't even know I did that", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 117 * 60000) },
          { sender: "girlfriend", text: "Oh really? So you just 'accidentally' scrolled through 2 years of her photos? 🙄", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 116 * 60000) },
          { sender: "boyfriend", text: "I was just looking at old group photos from college", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 115 * 60000) },
          { sender: "girlfriend", text: "At 2 AM? 🤔", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 114 * 60000) },
          { sender: "boyfriend", text: "I couldn't sleep, I was just scrolling", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 113 * 60000) },
          { sender: "girlfriend", text: "Right. And I suppose you couldn't sleep because you were thinking about her? 😠", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 112 * 60000) },
          { sender: "boyfriend", text: "You're being ridiculous right now", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 111 * 60000) },
          { sender: "girlfriend", text: "RIDICULOUS? You're the one stalking your ex at 2 AM! 😡", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 110 * 60000) },
          { sender: "boyfriend", text: "She's not my ex! We never dated!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 109 * 60000) },
          { sender: "girlfriend", text: "Oh, but you wanted to, didn't you? 🙄", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 108 * 60000) },
          { sender: "boyfriend", text: "This is insane. I'm not having this conversation", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 107 * 60000) },
          { sender: "girlfriend", text: "Of course, run away from your problems like always! 🏃‍♂️", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 106 * 60000) },
          { sender: "boyfriend", text: "I'm not running away, I'm just not engaging with this paranoia", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 105 * 60000) },
          { sender: "girlfriend", text: "PARANOIA? I have screenshots! ��", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 104 * 60000) },
          { sender: "boyfriend", text: "Of what? A liked photo? This is getting out of hand", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 103 * 60000) },
          { sender: "girlfriend", text: "You know what? I'm done. Have fun with Emily 👋", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 102 * 60000) },
          { sender: "boyfriend", text: "Don't be like this", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 101 * 60000) },
          { sender: "girlfriend", text: "Like what? Someone who cares about their relationship? 💔", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 100 * 60000) },
          { sender: "boyfriend", text: "You're twisting everything!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 99 * 60000) },
          { sender: "girlfriend", text: "I'm changing my relationship status to single. Hope it was worth it 😤", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 98 * 60000) },
          { sender: "boyfriend", text: "Over a liked photo? Are you serious right now?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 97 * 60000) },
          { sender: "girlfriend", text: "Don't text me again. I'm blocking you 🚫", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 96 * 60000) }
        ]
      },
      {
        id: "roommate-nightmare",
        title: "Roommate Nightmare",
        description: "When passive-aggressive notes about dishes turned into all-out war",
        timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 24 * 60 * 60 * 1000),
        messageCount: 20,
        image_url: "https://firebasestorage.googleapis.com/v0/b/text-drama.firebasestorage.app/o/public_images%2Fchat_roommate-nightmare.jpg?alt=media",
        messages: [
          { sender: "roommate1", text: "Hey, can we talk about the dishes situation?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 48 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "What about them?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.9 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "They've been piling up for a week... 🍽️", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.8 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "Most of them are yours", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.7 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "Are you kidding? I took photos every day this week! 📸", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.6 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "Wow, you're really that petty? 🙄", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.5 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "It's not petty to want a clean kitchen!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.4 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "What about your hair in the shower? 🚿", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.3 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "Don't change the subject!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.2 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "I'm not! You want to talk about cleanliness? Let's talk about EVERYTHING", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.1 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "Fine! Your gym clothes STINK up the entire living room! 🏋️‍♂️", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 47.0 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "At least I exercise! Unlike someone who watches Netflix all day", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.9 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "I WORK FROM HOME! 💻", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.8 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "Yeah, 'work'. I hear you playing video games all day", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.7 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "I'm a game developer! That IS my work! 🎮", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.6 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "Whatever. I'm done with this conversation", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.5 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "No, YOU'RE done. I'm calling the landlord tomorrow 📞", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.4 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "Go ahead! I'll tell them about your unauthorized pet hamster! 🐹", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.3 * 60 * 60 * 1000) },
          { sender: "roommate1", text: "Mr. Whiskers is an EMOTIONAL SUPPORT ANIMAL! 😤", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.2 * 60 * 60 * 1000) },
          { sender: "roommate2", text: "I'm moving out. Good luck paying double rent 👋", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 46.1 * 60 * 60 * 1000) }
        ]
      },
      {
        id: "wedding-drama",
        title: "Wedding Planning Drama",
        description: "When the bride found out her fiancé invited his ex to their wedding",
        timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 48 * 60 * 60 * 1000),
        messageCount: 22,
        image_url: "https://firebasestorage.googleapis.com/v0/b/text-drama.firebasestorage.app/o/public_images%2Fchat_wedding-drama.jpg?alt=media",
        messages: [
          { sender: "bride", text: "We need to talk about the guest list 📝", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 72 * 60 * 60 * 1000) },
          { sender: "groom", text: "What about it? I thought we finalized it last week", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.9 * 60 * 60 * 1000) },
          { sender: "bride", text: "Why is Jessica's name on it? 🤔", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.8 * 60 * 60 * 1000) },
          { sender: "groom", text: "Oh... she's part of the friend group from college", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.7 * 60 * 60 * 1000) },
          { sender: "bride", text: "Your EX is part of the friend group? 😠", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.6 * 60 * 60 * 1000) },
          { sender: "groom", text: "It was 5 years ago, we're all friends now", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.5 * 60 * 60 * 1000) },
          { sender: "bride", text: "All friends? When were you planning to tell me? 🤬", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.4 * 60 * 60 * 1000) },
          { sender: "groom", text: "I didn't think it was a big deal", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.3 * 60 * 60 * 1000) },
          { sender: "bride", text: "NOT A BIG DEAL? It's our WEDDING! 👰", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.2 * 60 * 60 * 1000) },
          { sender: "groom", text: "Exactly, it's OUR wedding. Can we be mature about this?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.1 * 60 * 60 * 1000) },
          { sender: "bride", text: "Oh, I'm immature now? For not wanting your ex at my wedding? 🙄", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 71.0 * 60 * 60 * 1000) },
          { sender: "groom", text: "Everyone's moved on. She's even engaged!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.9 * 60 * 60 * 1000) },
          { sender: "bride", text: "How do you know she's engaged? Still keeping tabs? 🕵️‍♀️", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.8 * 60 * 60 * 1000) },
          { sender: "groom", text: "It's on Facebook! Everyone knows!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.7 * 60 * 60 * 1000) },
          { sender: "bride", text: "Right. I'm calling the wedding planner 📞", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.6 * 60 * 60 * 1000) },
          { sender: "groom", text: "Why?", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.5 * 60 * 60 * 1000) },
          { sender: "bride", text: "To cancel everything! 🚫", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.4 * 60 * 60 * 1000) },
          { sender: "groom", text: "You're overreacting!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.3 * 60 * 60 * 1000) },
          { sender: "bride", text: "No, YOU'RE underreacting! This is a complete betrayal! 💔", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.2 * 60 * 60 * 1000) },
          { sender: "groom", text: "What about the deposits? We'll lose everything!", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.1 * 60 * 60 * 1000) },
          { sender: "bride", text: "Better than losing my dignity! Maybe Jessica can help you pay 💅", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 70.0 * 60 * 60 * 1000) },
          { sender: "groom", text: "I can't believe this is happening over a guest list...", timestamp: admin.firestore.Timestamp.fromMillis(Date.now() - 69.9 * 60 * 60 * 1000) }
        ]
      }
    ];

    // Delete existing data first
    const collections = ['chatPreviews', 'dramas'];
    for (const collectionName of collections) {
      const snapshot = await db.collection(collectionName).get();
      const batch = db.batch();
      snapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });
      await batch.commit();
    }

    // Add each chat and its messages to Firestore
    for (const chat of chats) {
      const { messages, ...chatPreview } = chat;

      // Add chat preview
      await db.collection('chatPreviews').doc(chat.id).set(chatPreview);

      // Add messages
      const messagesRef = db.collection('dramas').doc(chat.id).collection('messages');
      for (const message of messages) {
        await messagesRef.add(message);
      }
    }

    logger.info("Sample data created successfully!");
    response.json({ success: true, message: "Sample data created successfully!" });
  } catch (error) {
    logger.error("Error creating sample data:", error);
    response.status(500).json({ success: false, error: error.message });
  }
});
