import Foundation
import FirebaseFirestore
import Combine

class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()
    
    @Published var isCheckingSubscription = false
    private let db = Firestore.firestore()
    
    private init() {}
    
    /// Check if a specific chat requires a subscription
    func checkSubscriptionRequired(for chatId: String, completion: @escaping (Bool, Error?) -> Void) {
        isCheckingSubscription = true
        
        // First check in chatPreviews collection
        db.collection("chatPreviews").document(chatId).getDocument { [weak self] document, error in
            if let error = error {
                DispatchQueue.main.async {
                    self?.isCheckingSubscription = false
                    completion(false, error)
                }
                return
            }
            
            if let document = document, document.exists,
               let data = document.data(),
               let subscriptionRequired = data["subscription_required"] as? Bool {
                DispatchQueue.main.async {
                    self?.isCheckingSubscription = false
                    completion(subscriptionRequired, nil)
                }
                return
            }
            
            // If not found in chatPreviews, check in dramas collection
            self?.checkDramaSubscriptionRequired(chatId: chatId, completion: completion)
        }
    }
    
    private func checkDramaSubscriptionRequired(chatId: String, completion: @escaping (Bool, Error?) -> Void) {
        db.collection("dramas").document(chatId).getDocument { [weak self] document, error in
            DispatchQueue.main.async {
                self?.isCheckingSubscription = false
                
                if let error = error {
                    completion(false, error)
                    return
                }
                
                if let document = document, document.exists,
                   let data = document.data(),
                   let subscriptionRequired = data["subscription_required"] as? Bool {
                    completion(subscriptionRequired, nil)
                } else {
                    // Default to false if subscription_required field is not found
                    completion(false, nil)
                }
            }
        }
    }
    
    /// Check if user can access premium content
    func canAccessPremiumContent() -> Bool {
        return RevenueCatManager.shared.isSubscriptionActive
    }
    
    /// Determine if paywall should be shown for a specific chat
    func shouldShowPaywall(for chatId: String, completion: @escaping (Bool) -> Void) {
        // If user already has subscription, no need to show paywall
        if canAccessPremiumContent() {
            completion(false)
            return
        }
        
        // Check if this specific content requires subscription
        checkSubscriptionRequired(for: chatId) { subscriptionRequired, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error checking subscription requirement: \(error.localizedDescription)")
                    // Default to not showing paywall on error
                    completion(false)
                } else {
                    completion(subscriptionRequired)
                }
            }
        }
    }
    
    /// Get all chats that require subscription (for premium content filtering)
    func getPremiumChats(completion: @escaping ([String], Error?) -> Void) {
        db.collection("chatPreviews")
            .whereField("subscription_required", isEqualTo: true)
            .getDocuments { snapshot, error in
                if let error = error {
                    completion([], error)
                    return
                }
                
                let premiumChatIds = snapshot?.documents.compactMap { $0.documentID } ?? []
                completion(premiumChatIds, nil)
            }
    }
    
    /// Check subscription status and update UI accordingly
    func refreshSubscriptionStatus() {
        RevenueCatManager.shared.checkSubscriptionStatus()
    }
    
    /// Handle successful subscription purchase
    func handleSuccessfulPurchase() {
        // Refresh subscription status
        refreshSubscriptionStatus()
        
        // You can add additional logic here like:
        // - Showing success message
        // - Tracking analytics
        // - Updating user preferences
        
        print("Subscription purchase successful - access granted to premium content")
    }
    
    /// Handle subscription restoration
    func handleSuccessfulRestore() {
        // Refresh subscription status
        refreshSubscriptionStatus()
        
        print("Subscription restored successfully")
    }
    
    /// Get subscription status for UI display
    func getSubscriptionStatusText() -> String {
        if RevenueCatManager.shared.isSubscriptionActive {
            return "Premium Active"
        } else {
            return "Free Plan"
        }
    }
    
    /// Check if user needs to authenticate before showing paywall
    func requiresAuthentication() -> Bool {
        return !AuthenticationManager.shared.isAuthenticated
    }
}
