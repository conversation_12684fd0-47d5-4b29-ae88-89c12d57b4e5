import SwiftUI
import FirebaseStorage
import FirebaseFirestore

class FirebaseStorageService {
    static let shared = FirebaseStorageService()
    private let storage = Storage.storage()

    // Download image from Firebase Storage
    func downloadImage(from urlString: String, completion: @escaping (UIImage?) -> Void) {
        print("Attempting to download image from URL: \(urlString)")

        #if targetEnvironment(simulator)
        print("Running in iOS Simulator - checking network connectivity")
        #endif

        guard let url = URL(string: urlString) else {
            print("Invalid URL: \(urlString)")
            completion(nil)
            return
        }

        // Create a URLRequest with enhanced configuration for simulator compatibility
        var request = URLRequest(url: url)
        request.timeoutInterval = 30.0 // Increase timeout for simulator
        request.cachePolicy = .reloadIgnoringLocalCacheData // Bypass cache issues in simulator

        // Add user agent to avoid potential blocking
        request.setValue("Text-Drama-iOS-App/1.0", forHTTPHeaderField: "User-Agent")

        URLSession.shared.dataTask(with: request) { data, response, error in
            if let error = error {
                print("Error downloading image: \(error.localizedDescription)")
                #if targetEnvironment(simulator)
                print("Simulator network error details: \(error)")
                if let nsError = error as NSError? {
                    print("Error domain: \(nsError.domain), code: \(nsError.code)")
                    print("Error userInfo: \(nsError.userInfo)")
                }
                #endif
                completion(nil)
                return
            }

            if let httpResponse = response as? HTTPURLResponse {
                print("Image download response status code: \(httpResponse.statusCode)")
                print("Response headers: \(httpResponse.allHeaderFields)")

                if httpResponse.statusCode != 200 {
                    print("Failed to download image. Status code: \(httpResponse.statusCode)")
                    #if targetEnvironment(simulator)
                    print("Simulator: Non-200 status code might indicate network or ATS issues")
                    #endif
                    completion(nil)
                    return
                }
            }

            guard let data = data, let image = UIImage(data: data) else {
                print("Invalid image data or failed to create UIImage")
                #if targetEnvironment(simulator)
                print("Simulator: Data received but failed to create UIImage. Data size: \(data?.count ?? 0)")
                #endif
                completion(nil)
                return
            }

            print("Successfully downloaded image. Size: \(data.count) bytes")

            DispatchQueue.main.async {
                completion(image)
            }
        }.resume()
    }

    // Upload image to Firebase Storage and return the download URL
    func uploadImage(_ image: UIImage, path: String, completion: @escaping (String?) -> Void) {
        guard let imageData = image.jpegData(compressionQuality: 0.7) else {
            print("Could not convert image to data")
            completion(nil)
            return
        }

        let storageRef = storage.reference().child(path)

        storageRef.putData(imageData, metadata: nil) { metadata, error in
            if let error = error {
                print("Error uploading image: \(error.localizedDescription)")
                completion(nil)
                return
            }

            storageRef.downloadURL { url, error in
                if let error = error {
                    print("Error getting download URL: \(error.localizedDescription)")
                    completion(nil)
                    return
                }

                guard let downloadURL = url else {
                    print("Download URL is nil")
                    completion(nil)
                    return
                }

                completion(downloadURL.absoluteString)
            }
        }
    }
}
