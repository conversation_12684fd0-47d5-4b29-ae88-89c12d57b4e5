import Foundation
import RevenueCat
import FirebaseAuth

class RevenueCatManager: ObservableObject {
    static let shared = RevenueCatManager()
    
    @Published var isSubscriptionActive = false
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var isLoading = false
    
    private init() {
        configureRevenueCat()
    }
    
    private func configureRevenueCat() {
        // Configure RevenueCat with your API key
        Purchases.configure(withAPIKey: "appl_CAguqmmAEZbiUMdoZZsdxgVpCHv")
        
        // Set up delegate
        Purchases.shared.delegate = self
        
        // Enable debug logs in development
        #if DEBUG
        Purchases.logLevel = .debug
        #endif
        
        // Check initial subscription status
        checkSubscriptionStatus()
    }
    
    func setUserID(_ userID: String) {
        Purchases.shared.logIn(userID) { [weak self] customerInfo, created, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error logging in user to RevenueCat: \(error.localizedDescription)")
                } else {
                    print("Successfully logged in user to RevenueCat. Created: \(created)")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                }
            }
        }
    }
    
    func checkSubscriptionStatus() {
        isLoading = true
        
        Purchases.shared.getCustomerInfo { [weak self] customerInfo, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    print("Error fetching customer info: \(error.localizedDescription)")
                    self?.isSubscriptionActive = false
                } else if let customerInfo = customerInfo {
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                }
            }
        }
    }
    
    private func updateSubscriptionStatus(_ customerInfo: CustomerInfo?) {
        guard let customerInfo = customerInfo else {
            isSubscriptionActive = false
            return
        }
        
        // Check if user has any active entitlements
        isSubscriptionActive = !customerInfo.entitlements.active.isEmpty
        
        // You can also check for specific entitlements
        // isSubscriptionActive = customerInfo.entitlements["pro"]?.isActive == true
        
        print("Subscription status updated: \(isSubscriptionActive)")
    }
    
    func loadOfferings() {
        isLoading = true
        
        Purchases.shared.getOfferings { [weak self] offerings, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    print("Error fetching offerings: \(error.localizedDescription)")
                } else {
                    self?.offerings = offerings
                    print("Successfully loaded offerings")
                }
            }
        }
    }
    
    func purchasePackage(_ package: Package, completion: @escaping (Bool, Error?) -> Void) {
        isLoading = true
        
        Purchases.shared.purchase(package: package) { [weak self] transaction, customerInfo, error, userCancelled in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if userCancelled {
                    print("User cancelled purchase")
                    completion(false, nil)
                } else if let error = error {
                    print("Purchase error: \(error.localizedDescription)")
                    completion(false, error)
                } else {
                    print("Purchase successful!")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                    completion(true, nil)
                }
            }
        }
    }
    
    func restorePurchases(completion: @escaping (Bool, Error?) -> Void) {
        isLoading = true
        
        Purchases.shared.restorePurchases { [weak self] customerInfo, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    print("Restore purchases error: \(error.localizedDescription)")
                    completion(false, error)
                } else {
                    print("Purchases restored successfully")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                    completion(true, nil)
                }
            }
        }
    }
}

// MARK: - PurchasesDelegate
extension RevenueCatManager: PurchasesDelegate {
    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        DispatchQueue.main.async {
            self.customerInfo = customerInfo
            self.updateSubscriptionStatus(customerInfo)
        }
    }
}
