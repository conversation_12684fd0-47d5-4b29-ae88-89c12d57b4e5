import Foundation
import RevenueCat
import FirebaseAuth

class RevenueCatManager: NSObject, ObservableObject {
    static let shared = RevenueCatManager()

    @Published var isSubscriptionActive = false
    @Published var customerInfo: CustomerInfo?
    @Published var offerings: Offerings?
    @Published var isLoading = false

    override private init() {
        super.init()
        configureRevenueCat()
    }

    private func configureRevenueCat() {
        print("🚀 Configuring RevenueCat...")

        // Configure RevenueCat with your API key
        Purchases.configure(withAPIKey: "appl_CAguqmmAEZbiUMdoZZsdxgVpCHv")

        print("✅ RevenueCat configured with API key: appl_CAguqmmAEZbiUMdoZZsdxgVpCHv")

        // Set up delegate
        Purchases.shared.delegate = self

        // Enable debug logs in development
        #if DEBUG
        Purchases.logLevel = .debug
        print("🐛 Debug logging enabled")
        #endif

        // Check initial subscription status
        checkSubscriptionStatus()

        // Load offerings immediately
        loadOfferings()
    }

    func setUserID(_ userID: String) {
        Purchases.shared.logIn(userID) { [weak self] customerInfo, created, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error logging in user to RevenueCat: \(error.localizedDescription)")
                } else {
                    print("Successfully logged in user to RevenueCat. Created: \(created)")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                }
            }
        }
    }

    func checkSubscriptionStatus() {
        isLoading = true

        Purchases.shared.getCustomerInfo { [weak self] customerInfo, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    print("Error fetching customer info: \(error.localizedDescription)")
                    self?.isSubscriptionActive = false
                } else if let customerInfo = customerInfo {
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                }
            }
        }
    }

    private func updateSubscriptionStatus(_ customerInfo: CustomerInfo?) {
        guard let customerInfo = customerInfo else {
            isSubscriptionActive = false
            return
        }

        // Check for specific "Pro" entitlement
        isSubscriptionActive = customerInfo.entitlements["Pro"]?.isActive == true

        print("Subscription status updated: \(isSubscriptionActive)")
        print("Pro entitlement active: \(customerInfo.entitlements["Pro"]?.isActive ?? false)")

        // Log all active entitlements for debugging
        for (key, entitlement) in customerInfo.entitlements.active {
            print("Active entitlement: \(key) - \(entitlement.productIdentifier)")
        }
    }

    func loadOfferings() {
        isLoading = true

        print("🔄 Loading RevenueCat offerings...")

        Purchases.shared.getOfferings { [weak self] offerings, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    print("❌ Error fetching offerings: \(error.localizedDescription)")
                    print("Error details: \(error)")
                } else if let offerings = offerings {
                    self?.offerings = offerings
                    print("✅ Successfully loaded offerings")

                    // Debug: Print all offerings
                    print("📦 Total offerings: \(offerings.all.count)")

                    if let current = offerings.current {
                        print("📋 Current offering: \(current.identifier)")
                        print("📱 Available packages: \(current.availablePackages.count)")

                        for package in current.availablePackages {
                            print("  • \(package.storeProduct.productIdentifier): \(package.storeProduct.localizedTitle) - \(package.storeProduct.localizedPriceString)")
                            print("    Package Type: \(package.packageType)")
                            print("    Description: \(package.storeProduct.localizedDescription)")
                        }
                    } else {
                        print("⚠️ No current offering found")
                    }

                    // List all offerings
                    for (key, offering) in offerings.all {
                        print("🎯 Offering '\(key)': \(offering.availablePackages.count) packages")
                    }
                } else {
                    print("⚠️ Offerings is nil but no error")
                }
            }
        }
    }

    func purchasePackage(_ package: Package, completion: @escaping (Bool, Error?) -> Void) {
        isLoading = true

        Purchases.shared.purchase(package: package) { [weak self] transaction, customerInfo, error, userCancelled in
            DispatchQueue.main.async {
                self?.isLoading = false

                if userCancelled {
                    print("User cancelled purchase")
                    completion(false, nil)
                } else if let error = error {
                    print("Purchase error: \(error.localizedDescription)")
                    completion(false, error)
                } else {
                    print("Purchase successful!")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                    completion(true, nil)
                }
            }
        }
    }

    func restorePurchases(completion: @escaping (Bool, Error?) -> Void) {
        isLoading = true

        Purchases.shared.restorePurchases { [weak self] customerInfo, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    print("Restore purchases error: \(error.localizedDescription)")
                    completion(false, error)
                } else {
                    print("Purchases restored successfully")
                    self?.customerInfo = customerInfo
                    self?.updateSubscriptionStatus(customerInfo)
                    completion(true, nil)
                }
            }
        }
    }
}

// MARK: - PurchasesDelegate
extension RevenueCatManager: PurchasesDelegate {
    func purchases(_ purchases: Purchases, receivedUpdated customerInfo: CustomerInfo) {
        DispatchQueue.main.async {
            self.customerInfo = customerInfo
            self.updateSubscriptionStatus(customerInfo)
        }
    }
}
