//
//  ContentView.swift
//  Text_Drama
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/25.
//

import SwiftUI
import FirebaseFirestore
import UIKit

struct ContentView: View {
    @StateObject private var viewModel = ChatPreviewViewModel()
    @State private var selectedChatForNavigation: ChatPreview?
    @State private var showingDebugView = false
    @State private var showingPaywall = false
    @State private var paywallChatId: String?
    @Environment(\.colorScheme) var colorScheme
    @EnvironmentObject var authManager: AuthenticationManager
    @EnvironmentObject var revenueCatManager: RevenueCatManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager

    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 0) {
                    // Sliding card at the top
                    if !viewModel.chatPreviews.isEmpty {
                        SlidingCardView(chatPreviews: viewModel.chatPreviews) { chat in
                            selectedChatForNavigation = chat
                        }
                        .padding(.horizontal)
                        .padding(.top, 16)
                    }

                    // Category selector
                    CategorySelectorView(viewModel: viewModel)
                        .padding(.horizontal)
                        .padding(.top, 8)

                    // Main content
                    if viewModel.chatPreviews.isEmpty {
                        VStack {
                            Spacer()
                            Text("Loading chats...")
                                .font(.headline)
                            ProgressView()
                            Spacer()
                        }
                        .frame(minHeight: 300) // Give it some height for proper display
                    } else if viewModel.filteredChatPreviews.isEmpty {
                        VStack {
                            Spacer()
                            Image(systemName: "tray")
                                .font(.system(size: 50))
                                .foregroundColor(.gray)
                                .padding()
                            Text("No chats in this category")
                                .font(.headline)
                                .foregroundColor(.gray)
                            Spacer()
                        }
                        .frame(minHeight: 300) // Give it some height for proper display
                        .transition(.opacity)
                    } else {
                        // Chat list items
                        StaggeredList(items: viewModel.filteredChatPreviews) { chat in
                            Button(action: {
                                handleChatSelection(chat)
                            }) {
                                ChatCardView(chat: chat)
                                    .fixedSize(horizontal: false, vertical: true)
                                    .contentShape(Rectangle()) // Make entire area tappable
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding()
                        .transition(.opacity)
                    }
                }
            }
            .background(
                // Beautiful adaptive gradient for light and dark modes
                LinearGradient(
                    gradient: Gradient(colors: colorScheme == .dark ? [
                        // Dark mode: "Celestial" inspired - deep purple to navy
                        Color(red: 0.765, green: 0.216, blue: 0.392),  // #c33764 - Deep magenta
                        Color(red: 0.114, green: 0.149, blue: 0.443),  // #1d2671 - Deep navy blue
                        Color(red: 0.051, green: 0.051, blue: 0.118)   // #0d0d1e - Very dark blue
                    ] : [
                        // Light mode: "Sweet Morning" inspired - coral to peach
                        Color(red: 1.0, green: 0.373, blue: 0.427),    // #ff5f6d - Coral pink
                        Color(red: 1.0, green: 0.765, blue: 0.443),    // #ffc371 - Warm peach
                        Color(red: 1.0, green: 0.918, blue: 0.733)     // #ffebbb - Light cream
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
            )
            .background(
                // Hidden NavigationLink for sliding card navigation
                NavigationLink(
                    destination: selectedChatForNavigation.map { ChatDetailView(chatPreview: $0) },
                    isActive: Binding(
                        get: { selectedChatForNavigation != nil },
                        set: { if !$0 { selectedChatForNavigation = nil } }
                    )
                ) {
                    EmptyView()
                }
                .hidden()
            )
            .onAppear {
                print("ContentView appeared")
                print("Chat previews count: \(viewModel.chatPreviews.count)")

                // Force a refresh of the chat previews
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    print("Forcing refresh of chat previews")
                    viewModel.fetchChatPreviews()
                }
            }
            .networkDiagnostics()
            .toolbar {
                #if DEBUG && targetEnvironment(simulator)
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu("Debug") {
                        Button("Image Debug") {
                            showingDebugView = true
                        }
                        Button("RevenueCat Test") {
                            // Navigate to RevenueCat test view
                        }
                    }
                }
                #endif
            }
            .sheet(isPresented: $showingDebugView) {
                ImageLoadingDebugView()
            }
            .sheet(isPresented: $showingPaywall) {
                if let chatId = paywallChatId {
                    PaywallView(chatId: chatId)
                }
            }
        }
    }

    private func handleChatSelection(_ chat: ChatPreview) {
        guard let chatId = chat.id else {
            print("Chat ID is nil, cannot proceed")
            return
        }

        // Check if this chat requires subscription
        subscriptionManager.shouldShowPaywall(for: chatId) { shouldShow in
            if shouldShow {
                // Show paywall
                paywallChatId = chatId
                showingPaywall = true
            } else {
                // Navigate directly to chat
                selectedChatForNavigation = chat
            }
        }
    }
}

#Preview {
    ContentView()
}
