import SwiftUI

// A view modifier that animates a view's appearance
struct AnimatedAppearance: ViewModifier {
    var delay: Double
    @State private var opacity: Double = 0
    @State private var offset: CGFloat = 20
    
    func body(content: Content) -> some View {
        content
            .opacity(opacity)
            .offset(y: offset)
            .onAppear {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.75).delay(delay)) {
                    opacity = 1
                    offset = 0
                }
            }
    }
}

// Extension to make it easier to use
extension View {
    func animatedAppearance(delay: Double = 0) -> some View {
        self.modifier(AnimatedAppearance(delay: delay))
    }
}

// A staggered animation container for lists
struct StaggeredList<Content: View, T: Identifiable>: View {
    let items: [T]
    let spacing: CGFloat
    let content: (T) -> Content
    
    init(items: [T], spacing: CGFloat = 16, @ViewBuilder content: @escaping (T) -> Content) {
        self.items = items
        self.spacing = spacing
        self.content = content
    }
    
    var body: some View {
        LazyVStack(spacing: spacing) {
            ForEach(Array(items.enumerated()), id: \.element.id) { index, item in
                content(item)
                    .animatedAppearance(delay: Double(index) * 0.05)
            }
        }
    }
}
