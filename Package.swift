// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "Text_Drama",
    platforms: [
        .iOS(.v15)
    ],
    products: [
        .library(
            name: "Text_Drama",
            targets: ["Text_Drama"]),
    ],
    dependencies: [
        .package(url: "https://github.com/firebase/firebase-ios-sdk", from: "10.0.0"),
        .package(url: "https://github.com/RevenueCat/purchases-ios", from: "4.0.0")
    ],
    targets: [
        .target(
            name: "Text_Drama",
            dependencies: [
                .product(name: "FirebaseCore", package: "firebase-ios-sdk"),
                .product(name: "FirebaseFirestore", package: "firebase-ios-sdk"),
                .product(name: "FirebaseStorage", package: "firebase-ios-sdk"),
                .product(name: "FirebaseAuth", package: "firebase-ios-sdk"),
                .product(name: "RevenueCat", package: "purchases-ios")
            ]
        )
    ]
)
