import Foundation
import Network
import SwiftUI

class NetworkDiagnostics: ObservableObject {
    static let shared = NetworkDiagnostics()

    @Published var isConnected = false
    @Published var connectionType: NWInterface.InterfaceType?

    private let monitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")

    init() {
        startMonitoring()
    }

    private func startMonitoring() {
        monitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isConnected = path.status == .satisfied
                self?.connectionType = path.availableInterfaces.first?.type

                #if targetEnvironment(simulator)
                print("NetworkDiagnostics (Simulator): Connection status: \(path.status)")
                print("NetworkDiagnostics (Simulator): Available interfaces: \(path.availableInterfaces)")
                #endif
            }
        }
        monitor.start(queue: queue)
    }

    func testImageURL(_ urlString: String, completion: @escaping (Bool, String) -> Void) {
        guard let url = URL(string: urlString) else {
            completion(false, "Invalid URL")
            return
        }

        print("Testing image URL: \(urlString)")

        var request = URLRequest(url: url)
        request.timeoutInterval = 10.0
        request.httpMethod = "HEAD" // Just check if the resource exists

        URLSession.shared.dataTask(with: request) { _, response, error in
            DispatchQueue.main.async {
                if let error = error {
                    let errorMessage = "Network error: \(error.localizedDescription)"
                    print("URL test failed: \(errorMessage)")
                    completion(false, errorMessage)
                    return
                }

                if let httpResponse = response as? HTTPURLResponse {
                    let success = httpResponse.statusCode == 200
                    let message = success ? "URL accessible" : "HTTP \(httpResponse.statusCode)"
                    print("URL test result: \(message)")
                    completion(success, message)
                } else {
                    completion(false, "No HTTP response")
                }
            }
        }.resume()
    }

    private var connectionTypeString: String {
        guard let type = connectionType else { return "Unknown" }
        switch type {
        case .wifi:
            return "WiFi"
        case .cellular:
            return "Cellular"
        case .wiredEthernet:
            return "Ethernet"
        case .loopback:
            return "Loopback"
        case .other:
            return "Other"
        @unknown default:
            return "Unknown"
        }
    }

    func runDiagnostics(completion: @escaping (String) -> Void) {
        var diagnostics = "=== Network Diagnostics ===\n"

        #if targetEnvironment(simulator)
        diagnostics += "Environment: iOS Simulator\n"
        #else
        diagnostics += "Environment: Physical Device\n"
        #endif

        diagnostics += "Network Connected: \(isConnected)\n"
        diagnostics += "Connection Type: \(connectionTypeString)\n\n"

        // Test basic connectivity
        testImageURL("https://httpbin.org/status/200") { success, message in
            diagnostics += "Basic HTTPS Test: \(success ? "✅" : "❌") \(message)\n"

            // Test Unsplash
            self.testImageURL("https://images.unsplash.com/photo-1506905925346-21bda4d32df4") { success, message in
                diagnostics += "Unsplash Test: \(success ? "✅" : "❌") \(message)\n"

                // Test Unsplash Source (the problematic one)
                self.testImageURL("https://source.unsplash.com/800x600/?nature") { success, message in
                    diagnostics += "Unsplash Source Test: \(success ? "✅" : "❌") \(message)\n"

                    completion(diagnostics)
                }
            }
        }
    }
}

// Extension to add network diagnostics to any view
extension View {
    func networkDiagnostics() -> some View {
        self.onAppear {
            #if DEBUG && targetEnvironment(simulator)
            NetworkDiagnostics.shared.runDiagnostics { result in
                print(result)
            }
            #endif
        }
    }
}
