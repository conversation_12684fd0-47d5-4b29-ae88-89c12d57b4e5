import SwiftUI
import FirebaseFirestore

struct SlidingCardView: View {
    let chatPreviews: [ChatPreview]
    let onCardTapped: ((ChatPreview) -> Void)?
    @State private var currentIndex: Int = 0
    @State private var timer: Timer?
    @State private var dragOffset: CGFloat = 0
    @State private var isAnimating: Bool = false
    @State private var timerProgress: Double = 0.0

    // Default initializer without callback
    init(chatPreviews: [ChatPreview]) {
        self.chatPreviews = chatPreviews
        self.onCardTapped = nil
    }

    // Initializer with callback
    init(chatPreviews: [ChatPreview], onCardTapped: @escaping (ChatPreview) -> Void) {
        self.chatPreviews = chatPreviews
        self.onCardTapped = onCardTapped
    }

    private let slideInterval: TimeInterval = 6.0 // 6 seconds
    private let cardHeight: CGFloat = 220

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background cards for smooth transition - render in reverse order for proper layering
                ForEach(Array(chatPreviews.enumerated()).reversed(), id: \.element.id) { index, chat in
                    ChatCardView(chat: chat)
                        .frame(width: geometry.size.width, height: cardHeight)
                        .clipped() // Ensure card content doesn't overflow
                        .offset(x: offsetForIndex(index, in: geometry))
                        .opacity(opacityForIndex(index))
                        .scaleEffect(scaleForIndex(index))
                        .zIndex(zIndexForIndex(index))
                        .animation(.easeInOut(duration: 0.6), value: currentIndex)
                        .animation(.interactiveSpring(response: 0.3, dampingFraction: 0.8), value: dragOffset)
                        .onTapGesture {
                            // Only navigate if this is the current card and not dragging
                            if index == currentIndex && abs(dragOffset) < 10 {
                                onCardTapped?(chat)
                            }
                        }
                }

                // Slide indicators
                HStack(spacing: 8) {
                    ForEach(0..<min(chatPreviews.count, 5), id: \.self) { index in
                        Circle()
                            .fill(index == currentIndex % min(chatPreviews.count, 5) ? Color.primary : Color.secondary.opacity(0.3))
                            .frame(width: 8, height: 8)
                            .animation(.easeInOut(duration: 0.3), value: currentIndex)
                    }
                }
                .padding(.bottom, 16)
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
            }
        }
        .frame(height: cardHeight)
        .clipped()
        .simultaneousGesture(
            DragGesture(minimumDistance: 10)
                .onChanged { value in
                    // Make dragging more responsive by allowing real-time sliding
                    let translation = value.translation.width
                    dragOffset = translation
                    stopTimer()
                }
                .onEnded { value in
                    let threshold: CGFloat = 80 // Increased threshold for better UX
                    let velocity = value.predictedEndTranslation.width - value.translation.width

                    withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                        // Consider both distance and velocity for more natural feel
                        if value.translation.width > threshold || velocity > 100 {
                            // Swipe right - go to previous
                            goToPrevious()
                        } else if value.translation.width < -threshold || velocity < -100 {
                            // Swipe left - go to next
                            goToNext()
                        }
                        dragOffset = 0
                    }

                    // Always restart timer with full 6-second interval after manual interaction
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
                        restartTimer() // This ensures a fresh 6-second countdown
                    }
                }
        )
        .onAppear {
            print("SlidingCardView appeared - starting fresh timer")
            currentIndex = 0 // Always start from first card
            restartTimer()
        }
        .onDisappear {
            print("SlidingCardView disappeared - stopping timer")
            stopTimer()
        }
        .onChange(of: chatPreviews.count) { newCount in
            print("Chat previews count changed to: \(newCount)")
            // Reset to first card if data changes
            currentIndex = 0
            restartTimer()
        }
    }

    // MARK: - Helper Functions

    private func offsetForIndex(_ index: Int, in geometry: GeometryProxy) -> CGFloat {
        let distance = index - currentIndex

        if distance == 0 {
            // Current card - follows drag gesture
            return dragOffset * 0.8
        } else if distance == 1 {
            // Next card - starts from right and slides over current card
            let baseOffset = geometry.size.width
            let slideProgress = min(max(-dragOffset / geometry.size.width, 0), 1)
            return baseOffset - (slideProgress * geometry.size.width)
        } else if distance == -1 {
            // Previous card - starts from left
            let baseOffset = -geometry.size.width
            let slideProgress = min(max(dragOffset / geometry.size.width, 0), 1)
            return baseOffset + (slideProgress * geometry.size.width)
        } else {
            // Cards further away - keep them off screen
            return CGFloat(distance) * geometry.size.width
        }
    }

    private func zIndexForIndex(_ index: Int) -> Double {
        let distance = abs(index - currentIndex)
        if distance == 0 {
            return 1.0 // Current card in middle layer
        } else if distance == 1 {
            return 2.0 // Adjacent cards on top layer (slide over current)
        } else {
            return 0.0 // Other cards in background
        }
    }

    private func opacityForIndex(_ index: Int) -> Double {
        let distance = abs(index - currentIndex)
        if distance == 0 {
            return 1.0 // Current card fully visible
        } else if distance == 1 {
            return 1.0 // Adjacent cards also fully visible for smooth sliding
        } else {
            return 0.0 // Other cards hidden
        }
    }

    private func scaleForIndex(_ index: Int) -> CGFloat {
        let distance = abs(index - currentIndex)
        if distance == 0 {
            return 1.0 // Current card at full size
        } else if distance == 1 {
            return 1.0 // Adjacent cards also at full size for clean sliding
        } else {
            return 0.8 // Other cards smaller
        }
    }

    private func goToNext() {
        guard !chatPreviews.isEmpty else { return }

        withAnimation(.easeInOut(duration: 0.6)) {
            currentIndex = (currentIndex + 1) % chatPreviews.count
        }
        print("Moved to next card: \(currentIndex)")
    }

    private func goToPrevious() {
        guard !chatPreviews.isEmpty else { return }

        withAnimation(.easeInOut(duration: 0.6)) {
            currentIndex = currentIndex == 0 ? chatPreviews.count - 1 : currentIndex - 1
        }
        print("Moved to previous card: \(currentIndex)")
    }

    private func startTimer() {
        guard chatPreviews.count > 1 else { return }

        print("Starting timer for 6 seconds from card: \(currentIndex)")
        timerProgress = 0.0

        // Create a timer that updates every 0.1 seconds for smooth progress
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { timer in
            DispatchQueue.main.async {
                self.timerProgress += 0.1 / self.slideInterval

                if self.timerProgress >= 1.0 {
                    self.timerProgress = 0.0
                    self.goToNext()
                }
            }
        }
    }

    private func stopTimer() {
        print("Stopping timer")
        timer?.invalidate()
        timer = nil
        timerProgress = 0.0
    }

    private func restartTimer() {
        print("Restarting timer - fresh 6 second countdown")
        stopTimer()
        startTimer()
    }
}

// MARK: - Preview
struct SlidingCardView_Previews: PreviewProvider {
    static var previews: some View {
        SlidingCardView(chatPreviews: [
            ChatPreview(
                id: "1",
                title: "Men in Train",
                description: "AI generated text drama using Gemini",
                timestamp: Timestamp(date: Date()),
                messageCount: 27,
                categories: ["Drama", "Thriller"],
                image_url: "https://source.unsplash.com/random/800x600/?train",
                likeCount: 15,
                isLiked: true
            ),
            ChatPreview(
                id: "2",
                title: "Comedy Night",
                description: "Hilarious conversations between friends",
                timestamp: Timestamp(date: Date().addingTimeInterval(-3600)),
                messageCount: 15,
                categories: ["Comedy"],
                image_url: "https://source.unsplash.com/random/800x600/?comedy",
                likeCount: 8,
                isLiked: false
            ),
            ChatPreview(
                id: "3",
                title: "Sci-Fi Adventure",
                description: "Journey through space and time",
                timestamp: Timestamp(date: Date().addingTimeInterval(-7200)),
                messageCount: 42,
                categories: ["Sci-Fi"],
                image_url: "https://source.unsplash.com/random/800x600/?space",
                likeCount: 23,
                isLiked: true
            )
        ])
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
