import SwiftUI
import RevenueCat

struct RevenueCatTestView: View {
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    @State private var testChatId = "new-man" // Using the chat ID from your Firebase screenshot
    @State private var subscriptionRequired = false
    @State private var isCheckingSubscription = false
    @State private var showingPaywall = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // RevenueCat Status
                    GroupBox("RevenueCat Status") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Subscription Active:")
                                Spacer()
                                Text(revenueCatManager.isSubscriptionActive ? "✅ Yes" : "❌ No")
                                    .foregroundColor(revenueCatManager.isSubscriptionActive ? .green : .red)
                            }
                            
                            HStack {
                                Text("Customer Info:")
                                Spacer()
                                Text(revenueCatManager.customerInfo != nil ? "✅ Loaded" : "❌ Not Loaded")
                                    .foregroundColor(revenueCatManager.customerInfo != nil ? .green : .red)
                            }
                            
                            HStack {
                                Text("Offerings:")
                                Spacer()
                                Text(revenueCatManager.offerings != nil ? "✅ Loaded" : "❌ Not Loaded")
                                    .foregroundColor(revenueCatManager.offerings != nil ? .green : .red)
                            }
                            
                            if revenueCatManager.isLoading {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("Loading...")
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    
                    // Authentication Status
                    GroupBox("Authentication Status") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("User Authenticated:")
                                Spacer()
                                Text(authManager.isAuthenticated ? "✅ Yes" : "❌ No")
                                    .foregroundColor(authManager.isAuthenticated ? .green : .red)
                            }
                            
                            if let userProfile = authManager.userProfile {
                                HStack {
                                    Text("User Name:")
                                    Spacer()
                                    Text(userProfile.name)
                                        .foregroundColor(.secondary)
                                }
                                
                                HStack {
                                    Text("User Email:")
                                    Spacer()
                                    Text(userProfile.email)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    
                    // Test Subscription Check
                    GroupBox("Test Subscription Check") {
                        VStack(spacing: 12) {
                            HStack {
                                Text("Test Chat ID:")
                                TextField("Enter chat ID", text: $testChatId)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                            
                            Button(action: {
                                checkSubscriptionForChat()
                            }) {
                                HStack {
                                    if isCheckingSubscription {
                                        ProgressView()
                                            .scaleEffect(0.8)
                                    }
                                    Text("Check Subscription Required")
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(8)
                            }
                            .disabled(isCheckingSubscription || testChatId.isEmpty)
                            
                            if !isCheckingSubscription {
                                HStack {
                                    Text("Subscription Required:")
                                    Spacer()
                                    Text(subscriptionRequired ? "✅ Yes" : "❌ No")
                                        .foregroundColor(subscriptionRequired ? .orange : .green)
                                }
                            }
                        }
                    }
                    
                    // Action Buttons
                    VStack(spacing: 12) {
                        Button("Load Offerings") {
                            revenueCatManager.loadOfferings()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        
                        Button("Check Subscription Status") {
                            revenueCatManager.checkSubscriptionStatus()
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.orange)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        
                        Button("Show Test Paywall") {
                            showingPaywall = true
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.purple)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                        
                        Button("Restore Purchases") {
                            revenueCatManager.restorePurchases { success, error in
                                print("Restore result: \(success), error: \(error?.localizedDescription ?? "none")")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    
                    Spacer()
                }
                .padding()
            }
            .navigationTitle("RevenueCat Test")
            .onAppear {
                // Load offerings on appear
                revenueCatManager.loadOfferings()
            }
        }
        .sheet(isPresented: $showingPaywall) {
            PaywallView(chatId: testChatId)
        }
    }
    
    private func checkSubscriptionForChat() {
        isCheckingSubscription = true
        
        subscriptionManager.checkSubscriptionRequired(for: testChatId) { required, error in
            DispatchQueue.main.async {
                isCheckingSubscription = false
                
                if let error = error {
                    print("Error checking subscription: \(error.localizedDescription)")
                    subscriptionRequired = false
                } else {
                    subscriptionRequired = required
                    print("Subscription required for \(testChatId): \(required)")
                }
            }
        }
    }
}

#Preview {
    RevenueCatTestView()
}
