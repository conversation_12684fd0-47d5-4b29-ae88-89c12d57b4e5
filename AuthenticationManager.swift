import Foundation
import FirebaseAuth
import FirebaseFirestore
import Combine

struct UserProfile {
    let uid: String
    let email: String
    let name: String
    let dateOfBirth: Date
    let createdAt: Date
}

class AuthenticationManager: ObservableObject {
    static let shared = AuthenticationManager()
    
    @Published var isAuthenticated = false
    @Published var currentUser: User?
    @Published var userProfile: UserProfile?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let db = Firestore.firestore()
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Listen for authentication state changes
        Auth.auth().addStateDidChangeListener { [weak self] _, user in
            DispatchQueue.main.async {
                self?.currentUser = user
                self?.isAuthenticated = user != nil
                
                if let user = user {
                    self?.loadUserProfile(uid: user.uid)
                    // Set RevenueCat user ID
                    RevenueCatManager.shared.setUserID(user.uid)
                } else {
                    self?.userProfile = nil
                }
            }
        }
    }
    
    func signUp(email: String, password: String, name: String, dateOfBirth: Date, completion: @escaping (Bool, String?) -> Void) {
        isLoading = true
        errorMessage = nil
        
        Auth.auth().createUser(withEmail: email, password: password) { [weak self] result, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    let errorMsg = error.localizedDescription
                    self?.errorMessage = errorMsg
                    completion(false, errorMsg)
                    return
                }
                
                guard let user = result?.user else {
                    let errorMsg = "Failed to create user"
                    self?.errorMessage = errorMsg
                    completion(false, errorMsg)
                    return
                }
                
                // Save user profile to Firestore
                self?.saveUserProfile(
                    uid: user.uid,
                    email: email,
                    name: name,
                    dateOfBirth: dateOfBirth
                ) { success, error in
                    if success {
                        completion(true, nil)
                    } else {
                        completion(false, error)
                    }
                }
            }
        }
    }
    
    func signIn(email: String, password: String, completion: @escaping (Bool, String?) -> Void) {
        isLoading = true
        errorMessage = nil
        
        Auth.auth().signIn(withEmail: email, password: password) { [weak self] result, error in
            DispatchQueue.main.async {
                self?.isLoading = false
                
                if let error = error {
                    let errorMsg = error.localizedDescription
                    self?.errorMessage = errorMsg
                    completion(false, errorMsg)
                } else {
                    completion(true, nil)
                }
            }
        }
    }
    
    func signOut() {
        do {
            try Auth.auth().signOut()
            userProfile = nil
        } catch {
            print("Error signing out: \(error.localizedDescription)")
            errorMessage = error.localizedDescription
        }
    }
    
    private func saveUserProfile(uid: String, email: String, name: String, dateOfBirth: Date, completion: @escaping (Bool, String?) -> Void) {
        let userData: [String: Any] = [
            "uid": uid,
            "email": email,
            "name": name,
            "dateOfBirth": Timestamp(date: dateOfBirth),
            "createdAt": Timestamp(date: Date())
        ]
        
        db.collection("users").document(uid).setData(userData) { error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error saving user profile: \(error.localizedDescription)")
                    completion(false, error.localizedDescription)
                } else {
                    print("User profile saved successfully")
                    completion(true, nil)
                }
            }
        }
    }
    
    private func loadUserProfile(uid: String) {
        db.collection("users").document(uid).getDocument { [weak self] document, error in
            DispatchQueue.main.async {
                if let error = error {
                    print("Error loading user profile: \(error.localizedDescription)")
                    self?.errorMessage = error.localizedDescription
                    return
                }
                
                guard let document = document,
                      document.exists,
                      let data = document.data() else {
                    print("User profile document does not exist")
                    return
                }
                
                // Parse user profile data
                if let email = data["email"] as? String,
                   let name = data["name"] as? String,
                   let dobTimestamp = data["dateOfBirth"] as? Timestamp,
                   let createdAtTimestamp = data["createdAt"] as? Timestamp {
                    
                    self?.userProfile = UserProfile(
                        uid: uid,
                        email: email,
                        name: name,
                        dateOfBirth: dobTimestamp.dateValue(),
                        createdAt: createdAtTimestamp.dateValue()
                    )
                }
            }
        }
    }
    
    func updateUserProfile(name: String? = nil, completion: @escaping (Bool, String?) -> Void) {
        guard let uid = currentUser?.uid else {
            completion(false, "No authenticated user")
            return
        }
        
        var updateData: [String: Any] = [:]
        
        if let name = name {
            updateData["name"] = name
        }
        
        guard !updateData.isEmpty else {
            completion(true, nil)
            return
        }
        
        db.collection("users").document(uid).updateData(updateData) { [weak self] error in
            DispatchQueue.main.async {
                if let error = error {
                    completion(false, error.localizedDescription)
                } else {
                    // Reload user profile
                    self?.loadUserProfile(uid: uid)
                    completion(true, nil)
                }
            }
        }
    }
}
