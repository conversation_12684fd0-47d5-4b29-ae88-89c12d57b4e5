import SwiftUI
import FirebaseFirestore

struct MessageBubble: View {
    let message: Message
    let showSender: Bool
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 0) {
            // Show sender name only if showSender is true
            if showSender {
                HStack {
                    if isRightAligned {
                        Spacer()
                        Text(displayName)
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.trailing, 8)
                    } else {
                        Text(displayName)
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.leading, 8)
                        Spacer()
                    }
                }
                .padding(.top, 4)
                .padding(.bottom, 1)
            }

            HStack {
                if isRightAligned {
                    Spacer()
                    Text(message.text)
                        .padding(10)
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .clipShape(RoundedRectangle(cornerRadius: 18))
                } else {
                    Text(message.text)
                        .padding(10)
                        .background(Color(.systemGray5))
                        .foregroundColor(colorScheme == .dark ? .white : .black)
                        .clipShape(RoundedRectangle(cornerRadius: 18))
                    Spacer()
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, showSender ? 0 : 1) // Reduce padding when showing sender
    }

    // Get a clean display name without relationship labels
    private var displayName: String {
        // Capitalize the first letter for better presentation
        let firstChar = message.sender.prefix(1).uppercased()
        let restOfName = message.sender.dropFirst()

        // Return just the first part of compound names (before any numbers)
        if let range = restOfName.firstIndex(where: { !$0.isLetter }) {
            return firstChar + restOfName[..<range]
        }

        return firstChar + restOfName
    }

    // Determine if message should be right-aligned
    private var isRightAligned: Bool {
        message.sender.lowercased().contains("boyfriend") ||
        message.sender.lowercased().contains("husband") ||
        message.sender.lowercased().contains("roommate2") ||
        message.sender.lowercased().contains("groom")
    }
}

struct ChatDetailView: View {
    let chatPreview: ChatPreview
    @StateObject private var viewModel: ChatDetailViewModel
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showingPaywall = false

    init(chatPreview: ChatPreview) {
        self.chatPreview = chatPreview
        _viewModel = StateObject(wrappedValue: ChatDetailViewModel(chatId: chatPreview.id ?? ""))
    }

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 0) { // Remove default spacing
                ForEach(Array(viewModel.messages.enumerated()), id: \.element.id) { index, message in
                    let showSender = shouldShowSender(for: index)

                    // Add spacing before message if it's from a different sender
                    if showSender && index > 0 {
                        Spacer()
                            .frame(height: 8) // Add more space between different senders
                    }

                    MessageBubble(message: message, showSender: showSender)
                }
            }
        }
        .navigationTitle(chatPreview.title)
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            checkSubscriptionAccess()
        }
        .sheet(isPresented: $showingPaywall) {
            PaywallView(chatId: chatPreview.id ?? "")
        }
    }

    // Determine if we should show the sender name for this message
    private func shouldShowSender(for index: Int) -> Bool {
        guard index >= 0 && index < viewModel.messages.count else { return false }

        // Always show sender for the first message
        if index == 0 { return true }

        // Get current and previous message
        let currentMessage = viewModel.messages[index]
        let previousMessage = viewModel.messages[index - 1]

        // Show sender name if this is from a different sender than the previous message
        return currentMessage.sender != previousMessage.sender
    }

    private func checkSubscriptionAccess() {
        guard let chatId = chatPreview.id else { return }

        subscriptionManager.shouldShowPaywall(for: chatId) { shouldShow in
            if shouldShow {
                showingPaywall = true
            }
        }
    }
}