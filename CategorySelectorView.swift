import SwiftUI

struct CategorySelectorView: View {
    @ObservedObject var viewModel: ChatPreviewViewModel
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        VStack(spacing: 0) {
            // Title
            HStack {
                Text("Categories")
                    .font(.headline)
                    .foregroundColor(.secondary)
                    .padding(.leading)

                Spacer()
            }
            .padding(.top, 8)

            // Category buttons
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(viewModel.categories, id: \.self) { category in
                        CategoryButton(
                            category: category,
                            isSelected: viewModel.selectedCategory == category,
                            action: {
                                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                    viewModel.selectCategory(category)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(colorScheme == .dark ?
                      Color.black.opacity(0.4) : Color.white.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(colorScheme == .dark ?
                               Color.white.opacity(0.2) : Color.white.opacity(0.4),
                               lineWidth: 1)
                )
        )
    }
}

struct CategoryButton: View {
    let category: String
    let isSelected: Bool
    let action: () -> Void

    // Get a consistent color for each category
    private func categoryColor() -> Color {
        switch category.lowercased() {
        case "all": return .blue
        case "personal": return .blue
        case "work": return .green
        case "family": return .purple
        case "friends": return .orange
        case "important": return .red
        case "drama": return .purple
        case "thriller": return .red
        case "comedy": return .orange
        case "romance": return .pink
        case "sci-fi": return .blue
        case "horror": return .black
        case "uncategorized": return .gray
        default: return .gray
        }
    }

    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                // Icon based on category
                Image(systemName: categoryIcon())
                    .font(.system(size: 12))

                Text(category)
                    .font(.system(size: 14, weight: isSelected ? .bold : .medium))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? categoryColor() : Color(UIColor.systemGray6))
                    .overlay(
                        Capsule()
                            .strokeBorder(isSelected ? Color.clear : categoryColor().opacity(0.3), lineWidth: 1)
                    )
            )
            .foregroundColor(isSelected ? .white : categoryColor())
            .shadow(color: isSelected ? categoryColor().opacity(0.3) : Color.clear, radius: 4, x: 0, y: 2)
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
    }

    // Get an appropriate icon for each category
    private func categoryIcon() -> String {
        switch category.lowercased() {
        case "all": return "square.grid.2x2"
        case "personal": return "person"
        case "work": return "briefcase"
        case "family": return "house"
        case "friends": return "person.2"
        case "important": return "exclamationmark.circle"
        case "drama": return "theatermasks"
        case "thriller": return "bolt.fill"
        case "comedy": return "face.smiling"
        case "romance": return "heart"
        case "sci-fi": return "star"
        case "horror": return "eye"
        case "uncategorized": return "tag"
        default: return "tag"
        }
    }
}

struct CategorySelectorView_Previews: PreviewProvider {
    static var previews: some View {
        let viewModel = ChatPreviewViewModel()
        return CategorySelectorView(viewModel: viewModel)
            .previewLayout(.sizeThatFits)
    }
}
