import Foundation
import SwiftUI

struct ImageUtils {

    // Generate a relevant image URL based on chat content
    static func generateImageURLForChat(_ chat: ChatPreview) -> String {
        // If the chat already has an image URL, return it
        if let existingURL = chat.image_url, !existingURL.isEmpty {
            return existingURL
        }

        // Generate a search term based on the chat title and categories
        var searchTerm = chat.title.lowercased()

        // If the search term is too generic, use the category
        if searchTerm.count < 5 || searchTerm.contains("chat") {
            searchTerm = chat.primaryCategory.lowercased()
        }

        // Clean up the search term
        searchTerm = searchTerm.replacingOccurrences(of: " ", with: "+")

        #if targetEnvironment(simulator)
        // For simulator, use a more reliable Unsplash API endpoint
        // This endpoint is more stable and has better HTTPS support
        return "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center"
        #else
        // Use Unsplash source for random images related to the search term on real devices
        return "https://source.unsplash.com/random/800x600/?\(searchTerm)"
        #endif
    }

    // Update a chat with a relevant image URL if it doesn't already have one
    static func ensureChatHasImageURL(_ chat: inout ChatPreview) {
        if chat.image_url == nil || chat.image_url!.isEmpty {
            chat.image_url = generateImageURLForChat(chat)
        }
    }
}
