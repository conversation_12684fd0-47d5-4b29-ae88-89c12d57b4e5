import SwiftUI
import RevenueCat

struct PaywallView: View {
    @StateObject private var revenueCatManager = RevenueCatManager.shared
    @StateObject private var authManager = AuthenticationManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var selectedPackage: Package?
    @State private var showingSignup = false
    @State private var showingAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""
    @State private var isProcessingPurchase = false

    let chatId: String

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 12) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.yellow)

                        Text("Unlock Premium Content")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .multilineTextAlignment(.center)

                        Text("Get unlimited access to all premium drama conversations and exclusive content")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding(.top, 20)

                    // Features List
                    VStack(alignment: .leading, spacing: 16) {
                        FeatureRow(icon: "infinity", title: "Unlimited Access", description: "Read all premium drama conversations")
                        FeatureRow(icon: "star.fill", title: "Exclusive Content", description: "Access to subscriber-only stories")
                        FeatureRow(icon: "arrow.down.circle", title: "Offline Reading", description: "Download conversations for offline access")
                        FeatureRow(icon: "bell.fill", title: "Early Access", description: "Get new content before everyone else")
                    }
                    .padding(.horizontal)

                    // Subscription Packages
                    if let offerings = revenueCatManager.offerings,
                       let currentOffering = offerings.current {

                        VStack(spacing: 12) {
                            Text("Choose Your Plan")
                                .font(.headline)
                                .fontWeight(.semibold)

                            // Sort packages by duration (weekly, monthly, annual)
                            ForEach(sortedPackages(currentOffering.availablePackages), id: \.identifier) { package in
                                PackageView(
                                    package: package,
                                    isSelected: selectedPackage?.identifier == package.identifier,
                                    isRecommended: package.packageType == .annual
                                ) {
                                    selectedPackage = package
                                }
                            }
                        }
                        .padding(.horizontal)

                        // Purchase Button
                        if let selectedPackage = selectedPackage {
                            Button(action: {
                                purchasePackage(selectedPackage)
                            }) {
                                HStack {
                                    if isProcessingPurchase {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(0.8)
                                    }

                                    Text("Subscribe Now")
                                        .fontWeight(.semibold)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(12)
                            }
                            .disabled(isProcessingPurchase)
                            .padding(.horizontal)
                        }

                    } else {
                        // Loading state
                        VStack(spacing: 16) {
                            ProgressView()
                                .scaleEffect(1.2)
                            Text("Loading subscription options...")
                                .foregroundColor(.secondary)
                        }
                        .padding()
                    }

                    // Restore Purchases
                    Button(action: {
                        restorePurchases()
                    }) {
                        Text("Restore Purchases")
                            .foregroundColor(.blue)
                            .underline()
                    }
                    .disabled(isProcessingPurchase)

                    // Terms and Privacy
                    VStack(spacing: 8) {
                        Text("By subscribing, you agree to our Terms of Service and Privacy Policy")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        HStack(spacing: 20) {
                            Button("Terms of Service") {
                                // Open terms of service
                            }
                            .font(.caption)
                            .foregroundColor(.blue)

                            Button("Privacy Policy") {
                                // Open privacy policy
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal)
                    .padding(.bottom, 20)
                }
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            setupPaywall()
        }
        .sheet(isPresented: $showingSignup) {
            UserSignupView()
        }
        .alert(alertTitle, isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }

    private func setupPaywall() {
        // Check if user is authenticated
        if !authManager.isAuthenticated {
            showingSignup = true
            return
        }

        // Load offerings if not already loaded
        if revenueCatManager.offerings == nil {
            revenueCatManager.loadOfferings()
        }

        // Auto-select the annual package (recommended) if available, otherwise first package
        if let offerings = revenueCatManager.offerings,
           let currentOffering = offerings.current {
            let packages = currentOffering.availablePackages

            // Try to select annual package first (td_4999_1y)
            if let annualPackage = packages.first(where: { $0.packageType == .annual }) {
                selectedPackage = annualPackage
            } else if let firstPackage = packages.first {
                selectedPackage = firstPackage
            }

            // Debug: Print all available packages
            print("Available packages:")
            for package in packages {
                print("- \(package.storeProduct.productIdentifier): \(package.storeProduct.localizedTitle) - \(package.storeProduct.localizedPriceString)")
            }
        }
    }

    private func sortedPackages(_ packages: [Package]) -> [Package] {
        return packages.sorted { package1, package2 in
            // Sort by package type: weekly, monthly, annual
            let order1 = packageOrder(package1.packageType)
            let order2 = packageOrder(package2.packageType)
            return order1 < order2
        }
    }

    private func packageOrder(_ packageType: PackageType) -> Int {
        switch packageType {
        case .weekly: return 1
        case .monthly: return 2
        case .annual: return 3
        default: return 4
        }
    }

    private func purchasePackage(_ package: Package) {
        isProcessingPurchase = true

        revenueCatManager.purchasePackage(package) { success, error in
            isProcessingPurchase = false

            if success {
                SubscriptionManager.shared.handleSuccessfulPurchase()
                alertTitle = "Success!"
                alertMessage = "Welcome to Premium! You now have access to all premium content."
                showingAlert = true

                // Dismiss after showing success
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    dismiss()
                }
            } else if let error = error {
                alertTitle = "Purchase Failed"
                alertMessage = error.localizedDescription
                showingAlert = true
            }
        }
    }

    private func restorePurchases() {
        isProcessingPurchase = true

        revenueCatManager.restorePurchases { success, error in
            isProcessingPurchase = false

            if success {
                if revenueCatManager.isSubscriptionActive {
                    SubscriptionManager.shared.handleSuccessfulRestore()
                    alertTitle = "Restored!"
                    alertMessage = "Your subscription has been restored successfully."
                    showingAlert = true

                    // Dismiss after showing success
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        dismiss()
                    }
                } else {
                    alertTitle = "No Purchases Found"
                    alertMessage = "We couldn't find any previous purchases to restore."
                    showingAlert = true
                }
            } else if let error = error {
                alertTitle = "Restore Failed"
                alertMessage = error.localizedDescription
                showingAlert = true
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

struct PackageView: View {
    let package: Package
    let isSelected: Bool
    let isRecommended: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 0) {
                // Recommendation badge
                if isRecommended {
                    HStack {
                        Spacer()
                        Text("RECOMMENDED")
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.orange)
                            .cornerRadius(4)
                        Spacer()
                    }
                    .padding(.bottom, 8)
                }

                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(packageDisplayName)
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)

                            if isRecommended {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                            }
                        }

                        Text(packageDescription)
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        if package.packageType == .annual {
                            Text("Save 58% vs monthly")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                        }
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Text(package.storeProduct.localizedPriceString)
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.primary)

                        Text(pricePerPeriod)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .blue : .gray)
                        .font(.title2)
                }
                .padding()
            }
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : (isRecommended ? Color.orange : Color.gray.opacity(0.3)), lineWidth: isSelected ? 2 : (isRecommended ? 2 : 1))
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSelected ? Color.blue.opacity(0.1) : (isRecommended ? Color.orange.opacity(0.05) : Color.clear))
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var packageDisplayName: String {
        switch package.packageType {
        case .weekly: return "Weekly Plan"
        case .monthly: return "Monthly Plan"
        case .annual: return "Annual Plan"
        default: return package.storeProduct.localizedTitle
        }
    }

    private var packageDescription: String {
        switch package.packageType {
        case .weekly: return "Perfect for trying out"
        case .monthly: return "Great for regular users"
        case .annual: return "Best value for power users"
        default: return package.storeProduct.localizedDescription
        }
    }

    private var pricePerPeriod: String {
        switch package.packageType {
        case .weekly: return "per week"
        case .monthly: return "per month"
        case .annual: return "per year"
        default: return ""
        }
    }
}

#Preview {
    PaywallView(chatId: "sample-chat-id")
}
