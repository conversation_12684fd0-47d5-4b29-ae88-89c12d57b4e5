import Foundation
import FirebaseFirestore
import FirebaseStorage
import SwiftUI

class ChatPreviewViewModel: ObservableObject {
    @Published var chatPreviews: [ChatPreview] = []
    @Published var selectedCategory: String = "All"

    private let db = Firestore.firestore()

    init() {
        print("ChatPreviewViewModel initialized")
        fetchChatPreviews()
    }

    // Get all available categories from the chat previews
    var categories: [String] {
        var allCategories = Set<String>()

        // Add all categories from all chats
        for chat in chatPreviews {
            for category in chat.categories {
                allCategories.insert(category)
            }
            // If no categories, add "Uncategorized"
            if chat.categories.isEmpty {
                allCategories.insert("Uncategorized")
            }
        }

        allCategories.insert("All") // Always include "All" category
        return ["All"] + Array(allCategories).filter { $0 != "All" }.sorted()
    }

    // Get chat previews filtered by the selected category
    var filteredChatPreviews: [ChatPreview] {
        if selectedCategory == "All" {
            return chatPreviews
        } else {
            return chatPreviews.filter { chat in
                if chat.categories.isEmpty && selectedCategory == "Uncategorized" {
                    return true
                }
                return chat.categories.contains(selectedCategory)
            }
        }
    }

    // Group chat previews by primary category
    var groupedChatPreviews: [String: [ChatPreview]] {
        Dictionary(grouping: chatPreviews) { chat in
            return chat.primaryCategory
        }
    }

    func fetchChatPreviews() {
        print("Starting to fetch chat previews")
        db.collection("chatPreviews")
            .order(by: "timestamp", descending: true)
            .addSnapshotListener { [weak self] querySnapshot, error in
                guard let self = self else { return }

                if let error = error {
                    print("Error fetching documents: \(error.localizedDescription)")
                    return
                }

                guard let documents = querySnapshot?.documents else {
                    print("No documents found in chatPreviews collection")
                    return
                }

                print("Found \(documents.count) chat previews")

                self.chatPreviews = documents.compactMap { document in
                    do {
                        print("Processing document ID: \(document.documentID)")
                        let data = document.data()
                        print("Document data: \(data)")

                        var chat = try document.data(as: ChatPreview.self)
                        chat.id = document.documentID

                        // Ensure the chat has an image URL
                        ImageUtils.ensureChatHasImageURL(&chat)

                        print("Successfully decoded chat: \(chat.title)")
                        return chat
                    } catch {
                        print("Error decoding chat preview: \(error)")
                        return nil
                    }
                }

                print("Final chat previews count: \(self.chatPreviews.count)")
            }
    }

    // Set the selected category
    func selectCategory(_ category: String) {
        withAnimation {
            // If the user clicks on the currently selected category, reset to "All"
            if selectedCategory == category && category != "All" {
                selectedCategory = "All"
            } else {
                selectedCategory = category
            }
        }
    }
}
