rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to drama_index collection
    match /drama_index/{document=**} {
      allow read: if true;
      allow write: if false;
    }

    // Allow read access to dramas collection and its subcollections
    match /dramas/{dramaId} {
      allow read: if true;
      allow write: if false;

      match /messages/{messageId} {
        allow read: if true;
        allow write: if false;
      }
    }

    // Allow read access to chat previews
    match /chatPreviews/{document=**} {
      allow read: if true;   // Anyone can read chat previews
      allow write: if false; // No one can write to chat previews
    }

    // Allow read and write access to chat likes
    match /chatLikes/{document=**} {
      allow read, write: if true; // Anyone can read and write likes
    }

    // Default rule - deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}