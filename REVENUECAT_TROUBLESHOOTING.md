# RevenueCat Troubleshooting Guide

## 🚨 Issue: Can't See Payment Options

If you're not seeing the three payment options (Weekly, Monthly, Annual), here's how to debug and fix it:

## 🔍 Step 1: Check Console Logs

Run your app and look for these logs in Xcode console:

### Expected Logs:
```
🚀 Configuring RevenueCat...
✅ RevenueCat configured with API key: appl_CAguqmmAEZbiUMdoZZsdxgVpCHv
🐛 Debug logging enabled
🔄 Loading RevenueCat offerings...
✅ Successfully loaded offerings
📦 Total offerings: 1
📋 Current offering: default
📱 Available packages: 3
  • td_129_1w: Weekly Plan - $1.29
    Package Type: weekly
  • td_499_1m: Monthly Plan - $4.99
    Package Type: monthly
  • td_4999_1y: Annual Plan - $49.99
    Package Type: annual
```

### If You See Errors:
```
❌ Error fetching offerings: [Error message]
⚠️ No current offering found
📱 Available packages: 0
```

## 🔧 Step 2: Use Debug Interface

1. Run the app in simulator
2. Tap the "Debug" menu in the navigation bar
3. Select "RevenueCat Test"
4. Check the status indicators:
   - ✅ Subscription Active: Should show current status
   - ✅ Customer Info: Should be "Loaded"
   - ✅ Offerings: Should be "Loaded"
   - Available Products: Should list your 3 products

## 🛠️ Step 3: Common Issues & Solutions

### Issue 1: No Offerings Loaded
**Symptoms:** "❌ Not Loaded" for Offerings
**Causes:**
- RevenueCat dashboard not configured
- Products not added to offering
- API key incorrect

**Solutions:**
1. **Check RevenueCat Dashboard:**
   - Go to RevenueCat dashboard
   - Navigate to "Offerings" section
   - Ensure you have a "default" offering
   - Add your products: `td_129_1w`, `td_499_1m`, `td_4999_1y`

2. **Verify API Key:**
   - Confirm API key: `appl_CAguqmmAEZbiUMdoZZsdxgVpCHv`
   - Check it matches your RevenueCat project

### Issue 2: Products Not in App Store Connect
**Symptoms:** Products load but can't purchase
**Solutions:**
1. **App Store Connect Setup:**
   - Create in-app purchases with exact IDs:
     - `td_129_1w` (Auto-Renewable Subscription)
     - `td_499_1m` (Auto-Renewable Subscription)  
     - `td_4999_1y` (Auto-Renewable Subscription)
   - Submit for review and get approved
   - Use sandbox accounts for testing

### Issue 3: Entitlement Not Configured
**Symptoms:** Products load but subscription status wrong
**Solutions:**
1. **RevenueCat Entitlements:**
   - Create entitlement named exactly: `Pro`
   - Attach all three products to this entitlement
   - Save configuration

### Issue 4: Simulator vs Device Issues
**Symptoms:** Works on device but not simulator (or vice versa)
**Solutions:**
1. **For Simulator:**
   - Use sandbox Apple ID
   - Sign out of App Store in Settings
   - Sign in with sandbox account

2. **For Device:**
   - Ensure device is set to sandbox environment
   - Use TestFlight build or development build

## 🧪 Step 4: Manual Testing Steps

### Test 1: Basic Configuration
```swift
// In RevenueCatTestView, tap "Load Offerings"
// Check console for:
print("📦 Total offerings: \(offerings.all.count)")
// Should be > 0
```

### Test 2: Product Availability
```swift
// Look for your specific product IDs in console:
print("  • td_129_1w: Weekly Plan")
print("  • td_499_1m: Monthly Plan") 
print("  • td_4999_1y: Annual Plan")
```

### Test 3: Subscription Check
```swift
// In test view, enter chat ID: "new-man"
// Tap "Check Subscription Required"
// Should return true for premium content
```

## 🔧 Step 5: RevenueCat Dashboard Checklist

### Products Section:
- [ ] `td_129_1w` exists and is active
- [ ] `td_499_1m` exists and is active  
- [ ] `td_4999_1y` exists and is active
- [ ] All products are "Auto-Renewable Subscriptions"

### Entitlements Section:
- [ ] `Pro` entitlement exists
- [ ] All three products are attached to `Pro`

### Offerings Section:
- [ ] "default" offering exists
- [ ] All three products are in the offering
- [ ] Offering is marked as current

### App Settings:
- [ ] Bundle ID matches your app: `risul.rashed.com.Text-Drama`
- [ ] API key is correct: `appl_CAguqmmAEZbiUMdoZZsdxgVpCHv`

## 🚀 Step 6: Quick Fixes

### Force Reload Everything:
```swift
// In RevenueCatTestView:
1. Tap "Load Offerings" 
2. Tap "Check Subscription Status"
3. Check console logs
```

### Reset RevenueCat Cache:
```swift
// Add this to RevenueCatManager for testing:
Purchases.shared.invalidateCustomerInfoCache()
```

### Test with Different User:
```swift
// Sign out and sign in with different account
// Or use different sandbox Apple ID
```

## 📞 Still Having Issues?

### Check These Files:
1. **RevenueCatManager.swift** - Configuration and API key
2. **PaywallView.swift** - UI display logic
3. **Console Logs** - Detailed error messages

### Common Error Messages:
- `"No current offering found"` → Check RevenueCat dashboard offerings
- `"Error fetching offerings"` → Check API key and network
- `"Available packages: 0"` → Products not in offering

### Contact Points:
1. **RevenueCat Support** - For dashboard/configuration issues
2. **Apple Developer Support** - For App Store Connect issues
3. **Console Logs** - Always check these first!

## ✅ Success Indicators

You'll know it's working when you see:
1. **Console:** All 3 products listed with correct IDs
2. **PaywallView:** Shows 3 subscription options
3. **Test Interface:** All status indicators green
4. **Purchase Flow:** Can complete test purchases

The key is methodical debugging - start with console logs and work through each component! 🎯
