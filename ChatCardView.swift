import SwiftUI
import FirebaseFirestore
import FirebaseStorage

struct ChatCardView: View {
    @State var chat: ChatPreview
    @State private var isPressed: Bool = false
    @State private var isHovered: Bool = false
    @State private var backgroundBrightness: Double = 0.5 // Default to medium brightness
    @State private var isLiked: Bool = false
    @State private var likeCount: Int = 0
    @State private var isLikeAnimating: Bool = false
    @Environment(\.colorScheme) var colorScheme

    // Computed property to determine text color based on background brightness
    private var adaptiveTextColor: Color {
        // If brightness is above 0.5, use black text; otherwise use white text
        return backgroundBrightness > 0.5 ? .black : .white
    }

    // Function to calculate brightness of an image
    private func calculateImageBrightness(from image: UIImage) -> Double {
        guard let cgImage = image.cgImage else { return 0.5 }

        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        )

        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        var totalBrightness: Double = 0
        let pixelCount = width * height

        for i in stride(from: 0, to: pixelData.count, by: bytesPerPixel) {
            let red = Double(pixelData[i])
            let green = Double(pixelData[i + 1])
            let blue = Double(pixelData[i + 2])

            // Calculate perceived brightness using luminance formula
            let brightness = (0.299 * red + 0.587 * green + 0.114 * blue) / 255.0
            totalBrightness += brightness
        }

        return totalBrightness / Double(pixelCount)
    }

    // Function to simulate press effect that can be called from parent
    func simulatePress() {
        withAnimation(.easeInOut(duration: 0.1)) {
            isPressed = true
        }

        // Reset after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = false
            }
        }
    }

    // Get a gradient based on the category
    private var categoryGradient: LinearGradient {
        let baseColor = chat.categoryColor()
        return LinearGradient(
            gradient: Gradient(colors: [
                baseColor,
                baseColor.opacity(0.7)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // Get a gradient for a specific category
    private func categoryGradientFor(_ category: String) -> LinearGradient {
        let baseColor = colorForCategory(category)
        return LinearGradient(
            gradient: Gradient(colors: [
                baseColor,
                baseColor.opacity(0.7)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // Get color for a specific category
    private func colorForCategory(_ category: String) -> Color {
        switch category.lowercased() {
        case "personal": return .blue
        case "work": return .green
        case "family": return .purple
        case "friends": return .orange
        case "important": return .red
        case "drama": return .purple
        case "thriller": return .red
        case "comedy": return .orange
        case "romance": return .pink
        case "sci-fi": return .blue
        case "horror": return .black
        default: return .gray
        }
    }

    // Get icon for a specific category
    private func categoryIconFor(_ category: String) -> String {
        switch category.lowercased() {
        case "personal": return "person"
        case "work": return "briefcase"
        case "family": return "house"
        case "friends": return "person.2"
        case "important": return "exclamationmark.circle"
        case "drama": return "theatermasks"
        case "thriller": return "bolt.fill"
        case "comedy": return "face.smiling"
        case "romance": return "heart"
        case "sci-fi": return "star"
        case "horror": return "eye"
        default: return "tag"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Top section with categories and timestamp in the same row
            HStack(alignment: .center, spacing: 8) {
                // Categories on the left
                if !chat.categories.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 6) {
                            ForEach(chat.categories, id: \.self) { category in
                                // Category badge with blur background
                                HStack(spacing: 4) {
                                    Image(systemName: categoryIconFor(category))
                                        .font(.system(size: 10))
                                        .foregroundColor(.white)

                                    Text(category)
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)
                                        .lineLimit(1)
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(
                                    Capsule()
                                        .fill(categoryGradientFor(category))
                                        .shadow(color: .black.opacity(0.3), radius: 2, x: 0, y: 1)
                                )
                            }
                        }
                    }
                    .frame(height: 30)
                } else {
                    // Default category badge if no categories
                    HStack(spacing: 4) {
                        Image(systemName: "tag")
                            .font(.system(size: 10))
                            .foregroundColor(.white)

                        Text("Uncategorized")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .lineLimit(1)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [.gray, .gray.opacity(0.7)]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                    )
                    .frame(height: 30)
                }

                Spacer()

                // Timestamp on the right with blur background
                Text(chat.timestamp.dateValue(), style: .relative)
                    .font(.caption)
                    .foregroundColor(adaptiveTextColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        ZStack {
                            // Blurred background image
                            if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                                AsyncImageView(url: imageUrl, opacity: 1.0)
                                    .blur(radius: 10)
                                    .clipShape(Capsule())
                            }
                            // Semi-transparent overlay for better text readability
                            Capsule()
                                .fill(Color.black.opacity(0.0))
                        }
                    )
            }

            // Title with animation and blur background
            Text(chat.title)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(adaptiveTextColor)
                .lineLimit(1)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    ZStack {
                        // Blurred background image
                        if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                            AsyncImageView(url: imageUrl, opacity: 1.0)
                                .blur(radius: 10)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        // Semi-transparent overlay for better text readability
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.black.opacity(0.0))
                    }
                )
                .padding(.top, 4)
                .offset(x: isHovered ? 3 : 0)
                .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isHovered)

            // Description with blur background
            Text(chat.description)
                .font(.subheadline)
                .foregroundColor(adaptiveTextColor)
                .lineLimit(2)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    ZStack {
                        // Blurred background image
                        if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                            AsyncImageView(url: imageUrl, opacity: 1.0)
                                .blur(radius: 10)
                                .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        // Semi-transparent overlay for better text readability
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.black.opacity(0.0))
                    }
                )
                .padding(.bottom, 4)

            // Bottom section with message count, like button, and indicator
            HStack {
                // Message count with blur background
                HStack(spacing: 4) {
                    Image(systemName: "message")
                        .font(.system(size: 12))
                        .foregroundColor(adaptiveTextColor)

                    Text("\(chat.messageCount) messages")
                        .font(.caption)
                        .foregroundColor(adaptiveTextColor)
                }
                .padding(.horizontal, 10)
                .padding(.vertical, 4)
                .background(
                    ZStack {
                        // Blurred background image
                        if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                            AsyncImageView(url: imageUrl, opacity: 1.0)
                                .blur(radius: 10)
                                .clipShape(Capsule())
                        }
                        // Semi-transparent overlay for better text readability
                        Capsule()
                            .fill(Color.black.opacity(0.0))
                    }
                )

                Spacer()

                // Like button with blur background - positioned on the far right (like timestamp)
                Button(action: {
                    toggleLike()
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: isLiked ? "heart.fill" : "heart")
                            .font(.system(size: 12))
                            .foregroundColor(isLiked ? .red : adaptiveTextColor)
                            .scaleEffect(isLikeAnimating ? 1.3 : 1.0)
                            .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isLikeAnimating)

                        Text("\(likeCount)")
                            .font(.caption)
                            .foregroundColor(adaptiveTextColor)
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    ZStack {
                        // Blurred background image
                        if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                            AsyncImageView(url: imageUrl, opacity: 1.0)
                                .blur(radius: 10)
                                .clipShape(Capsule())
                        }
                        // Semi-transparent overlay for better text readability
                        Capsule()
                            .fill(Color.black.opacity(0.0))
                    }
                )
            }
        }
        .padding()
        .background(
            GeometryReader { geometry in
                ZStack {
                    // Background image if available (full opacity, no overlay)
                    if let imageUrl = chat.image_url, !imageUrl.isEmpty {
                        AsyncImageView(url: imageUrl, opacity: 1.0) { image in
                            // Calculate brightness when image loads
                            let brightness = calculateImageBrightness(from: image)
                            DispatchQueue.main.async {
                                backgroundBrightness = brightness
                            }
                        }
                        .clipShape(RoundedRectangle(cornerRadius: 16))
                        .frame(width: geometry.size.width, height: geometry.size.height)
                    } else {
                        // Fallback gradient background when no image
                        RoundedRectangle(cornerRadius: 16)
                            .fill(colorScheme == .dark ?
                                  Color.black.opacity(0.3) : Color.white.opacity(0.85))
                            .background(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(colorScheme == .dark ?
                                           Color.white.opacity(0.15) : Color.white.opacity(0.3),
                                           lineWidth: 1)
                            )
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .onAppear {
                                // Set brightness based on color scheme when no image
                                backgroundBrightness = colorScheme == .dark ? 0.2 : 0.8
                            }
                    }

                    // Category color indicator on the side
                    Rectangle()
                        .fill(chat.categoryColor())
                        .frame(width: 4)
                        .offset(x: -8, y: 0)
                        .mask(
                            RoundedRectangle(cornerRadius: 16)
                                .padding(.trailing, 16)
                        )
                }
                .frame(width: geometry.size.width, height: geometry.size.height)
            }
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isPressed)
        // Only use onHover on macOS
        #if os(macOS)
        .onHover { hovering in
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                isHovered = hovering
            }
        }
        #endif
        .onAppear {
            // Initialize like data from chat
            isLiked = chat.isLiked
            likeCount = chat.likeCount

            // Load like data from Firebase
            if let chatId = chat.id {
                LikeService.shared.getLikeData(for: chatId) { liked, count in
                    DispatchQueue.main.async {
                        isLiked = liked
                        likeCount = count
                    }
                }
            }
        }
    }

    // Toggle like function
    private func toggleLike() {
        guard let chatId = chat.id else { return }

        // Animate the heart
        withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
            isLikeAnimating = true
        }

        // Reset animation after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.6)) {
                isLikeAnimating = false
            }
        }

        // Update UI immediately for responsiveness
        let newIsLiked = !isLiked
        let newCount = newIsLiked ? likeCount + 1 : max(0, likeCount - 1)

        isLiked = newIsLiked
        likeCount = newCount

        // Update Firebase
        LikeService.shared.toggleLike(for: chatId) { finalIsLiked, finalCount in
            DispatchQueue.main.async {
                // Update with final values from Firebase
                isLiked = finalIsLiked
                likeCount = finalCount
            }
        }
    }

    // Get an appropriate icon for the category
    private func categoryIcon() -> String {
        switch chat.primaryCategory.lowercased() {
        case "personal": return "person"
        case "work": return "briefcase"
        case "family": return "house"
        case "friends": return "person.2"
        case "important": return "exclamationmark.circle"
        case "drama": return "theatermasks"
        case "thriller": return "bolt.fill"
        case "comedy": return "face.smiling"
        case "romance": return "heart"
        case "sci-fi": return "star"
        case "horror": return "eye"
        default: return "tag"
        }
    }
}

// Preview provider
struct ChatCardView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // Chat with multiple categories and image
            ChatCardView(chat: ChatPreview(
                id: "1",
                title: "Men in Train",
                description: "AI generated text drama using Gemini",
                timestamp: Timestamp(date: Date()),
                messageCount: 27,
                categories: ["Drama", "Thriller"],
                image_url: "https://source.unsplash.com/random/800x600/?train",
                likeCount: 15,
                isLiked: true
            ))

            // Chat with one category and image
            ChatCardView(chat: ChatPreview(
                id: "2",
                title: "Sample Chat",
                description: "This is a sample chat description that might be a bit longer to see how it wraps.",
                timestamp: Timestamp(date: Date().addingTimeInterval(-3600)),
                messageCount: 5,
                categories: ["Comedy"],
                image_url: "https://source.unsplash.com/random/800x600/?comedy",
                likeCount: 8,
                isLiked: false
            ))

            // Chat with no categories and no image
            ChatCardView(chat: ChatPreview(
                id: "3",
                title: "Uncategorized Chat",
                description: "This chat has no categories assigned.",
                timestamp: Timestamp(date: Date().addingTimeInterval(-7200)),
                messageCount: 3,
                categories: [],
                image_url: nil,
                likeCount: 2,
                isLiked: false
            ))
        }
        .previewLayout(.sizeThatFits)
        .padding()
    }
}
