import SwiftUI

struct AsyncImageView: View {
    let url: String?
    let contentMode: ContentMode
    let opacity: Double
    let onImageLoaded: ((UIImage) -> Void)?

    @State private var image: UIImage?
    @State private var isLoading = false

    init(url: String?, contentMode: ContentMode = .fill, opacity: Double = 0.5, onImageLoaded: ((UIImage) -> Void)? = nil) {
        self.url = url
        self.contentMode = contentMode
        self.opacity = opacity
        self.onImageLoaded = onImageLoaded
    }

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                if let image = image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: contentMode)
                        .opacity(opacity)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                } else {
                    Color.clear
                        .frame(width: geometry.size.width, height: geometry.size.height)

                    if isLoading {
                        ProgressView()
                    }
                }
            }
        }
        .onAppear {
            loadImage()
        }
        .onChange(of: url) { _ in
            loadImage()
        }
    }

    private func loadImage() {
        guard let urlString = url, !urlString.isEmpty else {
            print("AsyncImageView: URL is nil or empty")
            return
        }

        print("AsyncImageView: Loading image from URL: \(urlString)")
        isLoading = true

        FirebaseStorageService.shared.downloadImage(from: urlString) { downloadedImage in
            DispatchQueue.main.async {
                self.isLoading = false

                if let image = downloadedImage {
                    print("AsyncImageView: Successfully loaded image")
                    self.image = image
                    // Call the callback with the loaded image
                    self.onImageLoaded?(image)
                } else {
                    print("AsyncImageView: Failed to load image from URL: \(urlString)")
                    #if targetEnvironment(simulator)
                    print("AsyncImageView: Running in simulator - this might be a network configuration issue")
                    #endif
                }
            }
        }
    }
}

struct AsyncImageView_Previews: PreviewProvider {
    static var previews: some View {
        AsyncImageView(url: "https://example.com/image.jpg")
            .frame(width: 200, height: 200)
            .background(Color.gray.opacity(0.3))
            .previewLayout(.sizeThatFits)
    }
}
