import Foundation
import FirebaseFirestore

class ChatDetailViewModel: ObservableObject {
    @Published var messages: [Message] = []
    private let db = Firestore.firestore()
    private var chatId: String
    
    init(chatId: String) {
        self.chatId = chatId
        fetchMessages()
    }
    
    func fetchMessages() {
        db.collection("dramas")
            .document(chatId)
            .collection("messages")
            .order(by: "timestamp", descending: false)
            .addSnapshotListener { [weak self] querySnapshot, error in
                guard let self = self else { return }
                
                if let error = error {
                    print("Error fetching messages: \(error.localizedDescription)")
                    return
                }
                
                guard let documents = querySnapshot?.documents else {
                    print("No messages found")
                    return
                }
                
                self.messages = documents.compactMap { document in
                    do {
                        var message = try document.data(as: Message.self)
                        message.id = document.documentID
                        return message
                    } catch {
                        print("Error decoding message: \(error)")
                        return nil
                    }
                }
            }
    }
} 
